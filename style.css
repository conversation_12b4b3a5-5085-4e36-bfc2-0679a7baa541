* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.whatsapp-icon {
    font-size: 2.5rem;
    color: #25D366;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.whatsapp-icon:hover {
    transform: scale(1.2);
}

.title {
    font-size: 2rem;
    color: #2c3e50;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.login-icon {
    font-size: 2rem;
    color: #3498db;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.login-icon:hover {
    transform: scale(1.2);
}

/* Notifications Panel */
.notifications-panel {
    position: fixed;
    right: 20px;
    top: 100px;
    width: 300px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    z-index: 999;
    animation: slideInRight 1s ease-out;
}

.notifications-header {
    background: #3498db;
    color: white;
    padding: 15px;
    border-radius: 15px 15px 0 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: bold;
}

.notifications-list {
    padding: 15px;
}

.notification-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    margin-bottom: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.notification-item:hover {
    transform: translateX(-5px);
}

/* Main Content */
.main-content {
    margin-top: 120px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Animation Container */
.animation-container {
    width: 100%;
    max-width: 800px;
    height: 400px;
    position: relative;
    margin-bottom: 40px;
}

/* Church Scene */
.church-scene {
    width: 100%;
    height: 100%;
    position: relative;
    background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 70%, #90EE90 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Church Building */
.church {
    position: absolute;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    animation: churchGlow 3s ease-in-out infinite alternate;
}

.church-body {
    width: 120px;
    height: 100px;
    background: #8B4513;
    border-radius: 10px;
    position: relative;
}

.church-roof {
    width: 0;
    height: 0;
    border-left: 70px solid transparent;
    border-right: 70px solid transparent;
    border-bottom: 50px solid #654321;
    position: absolute;
    top: -50px;
    left: -10px;
}

.church-cross {
    position: absolute;
    top: -80px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 2rem;
    color: #FFD700;
    animation: crossGlow 2s ease-in-out infinite alternate;
}

.church-door {
    width: 30px;
    height: 50px;
    background: #654321;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 15px 15px 0 0;
}

.church-window {
    width: 20px;
    height: 20px;
    background: #FFD700;
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 50%;
    animation: windowGlow 1.5s ease-in-out infinite alternate;
}

/* Walking Person */
.person-walking {
    position: absolute;
    bottom: 50px;
    left: -100px;
    animation: walkTochurch 8s linear infinite;
}

.person {
    position: relative;
    animation: bobbing 0.5s ease-in-out infinite alternate;
}

.head {
    width: 20px;
    height: 20px;
    background: #FDBCB4;
    border-radius: 50%;
    margin: 0 auto;
}

.body {
    width: 15px;
    height: 30px;
    background: #4169E1;
    margin: 0 auto;
    border-radius: 5px;
}

.arm-left, .arm-right {
    width: 3px;
    height: 20px;
    background: #FDBCB4;
    position: absolute;
    top: 20px;
    animation: armSwing 0.5s ease-in-out infinite alternate;
}

.arm-left {
    left: 5px;
}

.arm-right {
    right: 5px;
}

.leg-left, .leg-right {
    width: 4px;
    height: 25px;
    background: #000080;
    position: absolute;
    top: 50px;
    animation: legWalk 0.5s ease-in-out infinite alternate;
}

.leg-left {
    left: 7px;
}

.leg-right {
    right: 7px;
    animation-delay: 0.25s;
}

/* Clouds */
.clouds {
    position: absolute;
    top: 20px;
    width: 100%;
}

.cloud {
    background: white;
    border-radius: 50px;
    opacity: 0.8;
    position: absolute;
}

.cloud1 {
    width: 80px;
    height: 40px;
    top: 20px;
    left: 10%;
    animation: cloudFloat 15s linear infinite;
}

.cloud2 {
    width: 60px;
    height: 30px;
    top: 50px;
    left: 70%;
    animation: cloudFloat 20s linear infinite reverse;
}

/* Birds */
.birds {
    position: absolute;
    top: 30px;
    width: 100%;
}

.bird {
    position: absolute;
    font-size: 1.5rem;
    animation: birdFly 12s linear infinite;
}

.bird1 {
    top: 40px;
    left: -50px;
    animation-delay: 0s;
}

.bird2 {
    top: 60px;
    left: -80px;
    animation-delay: 3s;
}

/* Welcome Message */
.welcome-message {
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 1s ease-out;
}

.welcome-message h2 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.welcome-message p {
    color: #7f8c8d;
    font-size: 1.2rem;
}

/* Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes walkTochurch {
    0% {
        left: -100px;
    }
    50% {
        left: 40%;
    }
    100% {
        left: -100px;
    }
}

@keyframes bobbing {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-3px);
    }
}

@keyframes armSwing {
    0% {
        transform: rotate(-10deg);
    }
    100% {
        transform: rotate(10deg);
    }
}

@keyframes legWalk {
    0% {
        transform: rotate(-15deg);
    }
    100% {
        transform: rotate(15deg);
    }
}

@keyframes cloudFloat {
    0% {
        transform: translateX(-100px);
    }
    100% {
        transform: translateX(calc(100vw + 100px));
    }
}

@keyframes birdFly {
    0% {
        transform: translateX(-50px) translateY(0);
    }
    25% {
        transform: translateX(25vw) translateY(-10px);
    }
    50% {
        transform: translateX(50vw) translateY(5px);
    }
    75% {
        transform: translateX(75vw) translateY(-5px);
    }
    100% {
        transform: translateX(calc(100vw + 50px)) translateY(0);
    }
}

@keyframes churchGlow {
    0% {
        filter: brightness(1);
    }
    100% {
        filter: brightness(1.1);
    }
}

@keyframes crossGlow {
    0% {
        text-shadow: 0 0 5px #FFD700;
    }
    100% {
        text-shadow: 0 0 20px #FFD700, 0 0 30px #FFD700;
    }
}

@keyframes windowGlow {
    0% {
        box-shadow: 0 0 5px #FFD700;
    }
    100% {
        box-shadow: 0 0 15px #FFD700, 0 0 25px #FFD700;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .notifications-panel {
        width: 250px;
        right: 10px;
    }
    
    .title {
        font-size: 1.5rem;
    }
    
    .animation-container {
        height: 300px;
    }
    
    .welcome-message h2 {
        font-size: 1.5rem;
    }
    
    .welcome-message p {
        font-size: 1rem;
    }
}
