* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.whatsapp-icon {
    font-size: 2.5rem;
    color: #25D366;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.whatsapp-icon:hover {
    transform: scale(1.2);
}

.title {
    font-size: 2rem;
    color: #2c3e50;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.login-icon {
    font-size: 2rem;
    color: #3498db;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.login-icon:hover {
    transform: scale(1.2);
}

/* Notifications Panel */
.notifications-panel {
    position: fixed;
    right: 20px;
    top: 100px;
    width: 300px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    z-index: 999;
    animation: slideInRight 1s ease-out;
}

.notifications-header {
    background: #3498db;
    color: white;
    padding: 15px;
    border-radius: 15px 15px 0 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: bold;
}

.notifications-list {
    padding: 15px;
}

.notification-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    margin-bottom: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.notification-item:hover {
    transform: translateX(-5px);
}

/* Main Content */
.main-content {
    margin-top: 120px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Animation Container */
.animation-container {
    width: 100%;
    max-width: 800px;
    height: 400px;
    position: relative;
    margin-bottom: 40px;
}

/* Church Scene */
.church-scene {
    width: 100%;
    height: 100%;
    position: relative;
    background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 70%, #90EE90 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Login Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #fefefe;
    margin: 10% auto;
    padding: 30px;
    border-radius: 20px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.5s ease-out;
}

.close {
    color: #aaa;
    float: left;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s;
}

.close:hover {
    color: #000;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #2c3e50;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #3498db;
}

#loginForm button {
    width: 100%;
    padding: 15px;
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.3s;
}

#loginForm button:hover {
    transform: translateY(-2px);
}

/* Church Sign */
.church-sign {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.sign-board {
    background: linear-gradient(45deg, #8B4513, #A0522D);
    color: #FFD700;
    padding: 15px 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border: 3px solid #FFD700;
    animation: signGlow 3s ease-in-out infinite alternate;
}

.sign-board h3 {
    font-size: 1.2rem;
    margin-bottom: 5px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.sign-board h4 {
    font-size: 1rem;
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Realistic Church Building */
.church {
    position: absolute;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    animation: churchGlow 4s ease-in-out infinite alternate;
}

.church-main {
    position: relative;
    width: 200px;
    height: 150px;
}

.church-wall {
    width: 200px;
    height: 120px;
    background: linear-gradient(45deg, #D2B48C, #DEB887);
    border-radius: 10px;
    position: relative;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    border: 2px solid #8B7355;
}

.church-entrance {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

.entrance-arch {
    width: 50px;
    height: 70px;
    background: linear-gradient(180deg, #654321, #8B4513);
    border-radius: 25px 25px 0 0;
    position: relative;
    border: 2px solid #4A4A4A;
}

.entrance-door {
    width: 40px;
    height: 60px;
    background: linear-gradient(180deg, #8B4513, #654321);
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 20px 20px 0 0;
    border: 1px solid #2F2F2F;
}

.entrance-steps {
    width: 60px;
    height: 8px;
    background: #A9A9A9;
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.church-window {
    position: absolute;
    width: 25px;
    height: 35px;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    border-radius: 12px 12px 0 0;
    border: 2px solid #8B4513;
    animation: windowGlow 2s ease-in-out infinite alternate;
}

.window-1 {
    top: 30px;
    left: 30px;
}

.window-2 {
    top: 30px;
    right: 30px;
}

.window-3 {
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 30px;
}

.church-tower {
    position: absolute;
    top: -80px;
    left: 50%;
    transform: translateX(-50%);
}

.tower-body {
    width: 40px;
    height: 80px;
    background: linear-gradient(45deg, #D2B48C, #DEB887);
    border-radius: 5px;
    border: 2px solid #8B7355;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.tower-roof {
    width: 0;
    height: 0;
    border-left: 25px solid transparent;
    border-right: 25px solid transparent;
    border-bottom: 30px solid #8B4513;
    position: absolute;
    top: -30px;
    left: -5px;
}

.tower-cross {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 2.5rem;
    color: #FFD700;
    text-shadow: 0 0 10px #FFD700;
    animation: crossGlow 3s ease-in-out infinite alternate;
}

.tower-bell {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.5rem;
    animation: bellSwing 4s ease-in-out infinite;
}

.interior-glow {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 120px;
    background: radial-gradient(ellipse at center bottom, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
    border-radius: 10px;
    animation: interiorGlow 3s ease-in-out infinite alternate;
}

.candles {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
}

.candle {
    position: relative;
    animation: candleFlicker 2s ease-in-out infinite alternate;
}

.candle-body {
    width: 8px;
    height: 25px;
    background: linear-gradient(180deg, #F5DEB3, #DEB887);
    border-radius: 4px;
    border: 1px solid #8B7355;
}

.candle-flame {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1rem;
    animation: flameFlicker 1s ease-in-out infinite alternate;
}

.candle-2 {
    animation-delay: 0.5s;
}

.candle-3 {
    animation-delay: 1s;
}

/* Walking Person with Tunic */
.person-walking {
    position: absolute;
    bottom: 50px;
    left: -100px;
    animation: walkTochurch 12s linear infinite;
    z-index: 5;
}

.person {
    position: relative;
    animation: bobbing 0.6s ease-in-out infinite alternate;
}

.head {
    width: 22px;
    height: 22px;
    background: radial-gradient(circle, #FDBCB4, #F4A460);
    border-radius: 50%;
    margin: 0 auto;
    border: 1px solid #D2B48C;
    position: relative;
}

.head::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 18px;
    height: 10px;
    background: #8B4513;
    border-radius: 10px 10px 0 0;
}

.tunic {
    width: 25px;
    height: 45px;
    background: linear-gradient(180deg, #8B4513, #A0522D);
    margin: 0 auto;
    border-radius: 5px;
    position: relative;
    border: 1px solid #654321;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.tunic-belt {
    position: absolute;
    top: 35px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 4px;
    background: #654321;
    border-radius: 2px;
}

.arm-left, .arm-right {
    width: 4px;
    height: 22px;
    background: linear-gradient(180deg, #FDBCB4, #F4A460);
    position: absolute;
    top: 25px;
    border-radius: 2px;
    animation: armSwing 0.6s ease-in-out infinite alternate;
}

.arm-left {
    left: 3px;
    animation-delay: 0.3s;
}

.arm-right {
    right: 3px;
}

.leg-left, .leg-right {
    width: 5px;
    height: 28px;
    background: linear-gradient(180deg, #8B4513, #654321);
    position: absolute;
    top: 65px;
    border-radius: 2px;
    animation: legWalk 0.6s ease-in-out infinite alternate;
}

.leg-left {
    left: 8px;
}

.leg-right {
    right: 8px;
    animation-delay: 0.3s;
}

.prayer-book {
    position: absolute;
    top: 30px;
    right: -8px;
    font-size: 0.8rem;
    animation: bookHold 2s ease-in-out infinite alternate;
}

/* Holy Light Effect */
.holy-light {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 300px;
    height: 200px;
    background: radial-gradient(ellipse at center, rgba(255, 215, 0, 0.2) 0%, transparent 70%);
    animation: holyLightPulse 4s ease-in-out infinite alternate;
    pointer-events: none;
}

/* Clouds */
.clouds {
    position: absolute;
    top: 20px;
    width: 100%;
}

.cloud {
    background: white;
    border-radius: 50px;
    opacity: 0.8;
    position: absolute;
}

.cloud1 {
    width: 80px;
    height: 40px;
    top: 20px;
    left: 10%;
    animation: cloudFloat 15s linear infinite;
}

.cloud2 {
    width: 60px;
    height: 30px;
    top: 50px;
    left: 70%;
    animation: cloudFloat 20s linear infinite reverse;
}

/* Birds */
.birds {
    position: absolute;
    top: 30px;
    width: 100%;
}

.bird {
    position: absolute;
    font-size: 1.5rem;
    animation: birdFly 12s linear infinite;
}

.bird1 {
    top: 40px;
    left: -50px;
    animation-delay: 0s;
}

.bird2 {
    top: 60px;
    left: -80px;
    animation-delay: 3s;
}

/* Welcome Message */
.welcome-message {
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 1s ease-out;
}

.welcome-message h2 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.welcome-message p {
    color: #7f8c8d;
    font-size: 1.2rem;
}

/* Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes walkTochurch {
    0% {
        left: -100px;
    }
    50% {
        left: 40%;
    }
    100% {
        left: -100px;
    }
}

@keyframes bobbing {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-3px);
    }
}

@keyframes armSwing {
    0% {
        transform: rotate(-10deg);
    }
    100% {
        transform: rotate(10deg);
    }
}

@keyframes legWalk {
    0% {
        transform: rotate(-15deg);
    }
    100% {
        transform: rotate(15deg);
    }
}

@keyframes cloudFloat {
    0% {
        transform: translateX(-100px);
    }
    100% {
        transform: translateX(calc(100vw + 100px));
    }
}

@keyframes birdFly {
    0% {
        transform: translateX(-50px) translateY(0);
    }
    25% {
        transform: translateX(25vw) translateY(-10px);
    }
    50% {
        transform: translateX(50vw) translateY(5px);
    }
    75% {
        transform: translateX(75vw) translateY(-5px);
    }
    100% {
        transform: translateX(calc(100vw + 50px)) translateY(0);
    }
}

@keyframes churchGlow {
    0% {
        filter: brightness(1);
    }
    100% {
        filter: brightness(1.1);
    }
}

@keyframes crossGlow {
    0% {
        text-shadow: 0 0 5px #FFD700;
    }
    100% {
        text-shadow: 0 0 20px #FFD700, 0 0 30px #FFD700;
    }
}

@keyframes windowGlow {
    0% {
        box-shadow: 0 0 5px #FFD700;
    }
    100% {
        box-shadow: 0 0 15px #FFD700, 0 0 25px #FFD700;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes signGlow {
    0% {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 215, 0, 0.3);
    }
    100% {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 40px rgba(255, 215, 0, 0.6);
    }
}

@keyframes bellSwing {
    0%, 100% {
        transform: translateX(-50%) rotate(-5deg);
    }
    50% {
        transform: translateX(-50%) rotate(5deg);
    }
}

@keyframes interiorGlow {
    0% {
        opacity: 0.3;
    }
    100% {
        opacity: 0.7;
    }
}

@keyframes candleFlicker {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-2px);
    }
}

@keyframes flameFlicker {
    0% {
        transform: translateX(-50%) scale(1);
        filter: hue-rotate(0deg);
    }
    100% {
        transform: translateX(-50%) scale(1.1);
        filter: hue-rotate(10deg);
    }
}

@keyframes bookHold {
    0% {
        transform: rotate(-5deg);
    }
    100% {
        transform: rotate(5deg);
    }
}

@keyframes holyLightPulse {
    0% {
        opacity: 0.2;
        transform: translateX(-50%) scale(1);
    }
    100% {
        opacity: 0.5;
        transform: translateX(-50%) scale(1.1);
    }
}

/* App Navigation */
.app-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 15px 20px;
    max-width: 600px;
    margin: 0 auto;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 10px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #7f8c8d;
}

.nav-item.active {
    color: #3498db;
    background: rgba(52, 152, 219, 0.1);
}

.nav-item:hover {
    color: #3498db;
    transform: translateY(-2px);
}

.nav-item i {
    font-size: 1.5rem;
}

.nav-item span {
    font-size: 0.8rem;
    font-weight: bold;
}

/* App Container */
.app-container {
    margin-top: 80px;
    padding: 20px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.app-section {
    min-height: calc(100vh - 120px);
}

/* Daily Verse */
.daily-verse {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 20px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    text-align: center;
}

.verse-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
}

.verse-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.verse-content {
    margin-bottom: 20px;
}

.verse-text {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 10px;
    font-style: italic;
}

.verse-ref {
    font-weight: bold;
    color: #FFD700;
}

.new-verse-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.new-verse-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Post Creator */
.post-creator {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.post-input-container {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.user-avatar-small {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

#postInput {
    flex: 1;
    border: 2px solid #ecf0f1;
    border-radius: 25px;
    padding: 15px 20px;
    font-size: 1rem;
    resize: none;
    min-height: 50px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

#postInput:focus {
    outline: none;
    border-color: #3498db;
}

.post-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.post-btn, .verse-btn, .prayer-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.post-btn {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.verse-btn {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
}

.prayer-btn {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.post-btn:hover, .verse-btn:hover, .prayer-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Posts Feed */
.posts-feed {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.post-item {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.post-item:hover {
    transform: translateY(-2px);
}

.post-header {
    margin-bottom: 15px;
}

.post-user {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
}

.user-info h4 {
    margin: 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

.verified {
    color: #000;
    font-size: 1rem;
}

.post-time {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.post-content {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #2c3e50;
}

.post-interactions {
    display: flex;
    gap: 15px;
    padding-top: 15px;
    border-top: 1px solid #ecf0f1;
}

.interaction-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    border: none;
    border-radius: 20px;
    background: #f8f9fa;
    color: #7f8c8d;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.interaction-btn:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.interaction-btn.active {
    background: #3498db;
    color: white;
}

.like-btn.active {
    background: #e74c3c;
    color: white;
}

.pray-btn.active {
    background: #f39c12;
    color: white;
}

/* Profile Section */
.profile-container {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.profile-header {
    position: relative;
}

.cover-photo {
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.change-cover-btn {
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.change-cover-btn:hover {
    background: rgba(0, 0, 0, 0.7);
}

.profile-info {
    padding: 20px;
    text-align: center;
    position: relative;
    margin-top: -50px;
}

.profile-picture {
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, #3498db, #2980b9);
    border-radius: 50%;
    margin: 0 auto 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
    border: 5px solid white;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.change-pic-btn {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 30px;
    height: 30px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

#profileName {
    margin: 0 0 10px 0;
    color: #2c3e50;
}

.verification-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: #000;
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.9rem;
    margin: 10px auto;
    width: fit-content;
}

.profile-details {
    padding: 20px;
    border-top: 1px solid #ecf0f1;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #f8f9fa;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item i {
    color: #3498db;
    width: 20px;
}

.detail-item span {
    flex: 1;
    color: #7f8c8d;
}

.detail-item button {
    background: none;
    border: none;
    color: #3498db;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.detail-item button:hover {
    background: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .notifications-panel {
        width: 250px;
        right: 10px;
    }

    .title {
        font-size: 1.5rem;
    }

    .animation-container {
        height: 300px;
    }

    .welcome-message h2 {
        font-size: 1.5rem;
    }

    .welcome-message p {
        font-size: 1rem;
    }

    .app-container {
        padding: 10px;
    }

    .post-actions {
        flex-direction: column;
    }

    .post-interactions {
        flex-wrap: wrap;
        gap: 10px;
    }

    .interaction-btn {
        flex: 1;
        justify-content: center;
        min-width: 80px;
    }
}
