// Registered emails storage
let registeredEmails = JSON.parse(localStorage.getItem('registeredEmails')) || [];

// Wait for DOM to load
document.addEventListener('DOMContentLoaded', function() {

    // WhatsApp icon click handler
    const whatsappIcon = document.querySelector('.whatsapp-icon');
    whatsappIcon.addEventListener('click', function() {
        // Add click animation
        this.style.transform = 'scale(1.3)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 200);

        // Show detailed team message
        const teamMessage = `
مرحباً بك! 👋

نحن فريق برمجة المستقبل - الشبكة الاجتماعية المسيحية الأولى من نوعها!

🌟 ما نقدمه:
• منصة آمنة للتواصل بين المسيحيين
• مشاركة الآيات والتأملات الروحية
• طلبات الصلاة والدعم الروحي
• مجتمع محب ومتفاهم

👨‍💻 فريق العمل:
• المطور الرئيسي: Beshoy 586
• مطورين متخصصين في التقنيات الحديثة
• فريق دعم فني متاح 24/7

📱 تواصل معنا الآن للاستفسارات أو الدعم التقني!
        `;

        showMessage('سيتم توجيهك لواتساب الآن...', 'success');

        // Open WhatsApp with detailed message
        setTimeout(() => {
            const encodedMessage = encodeURIComponent(teamMessage);
            window.open(`https://wa.me/201147150701?text=${encodedMessage}`, '_blank');
        }, 1000);
    });

    // Login icon click handler
    const loginIcon = document.querySelector('.login-icon');
    const loginModal = document.getElementById('loginModal');
    const closeModal = document.querySelector('.close');

    loginIcon.addEventListener('click', function() {
        // Add click animation
        this.style.transform = 'scale(1.3)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 200);

        // Show login modal
        loginModal.style.display = 'block';
    });

    // Close modal handlers
    closeModal.addEventListener('click', function() {
        loginModal.style.display = 'none';
    });

    window.addEventListener('click', function(event) {
        if (event.target === loginModal) {
            loginModal.style.display = 'none';
        }
    });

    // Login form handler
    const loginForm = document.getElementById('loginForm');
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const userName = document.getElementById('userName').value.trim();
        const userEmail = document.getElementById('userEmail').value.trim();

        // Validate inputs
        if (!userName || !userEmail) {
            showMessage('يرجى ملء جميع الحقول', 'error');
            return;
        }

        // Check if email is already registered
        if (registeredEmails.includes(userEmail.toLowerCase())) {
            showMessage('هذا البريد الإلكتروني مسجل مسبقاً!', 'error');
            return;
        }

        // Register the email
        registeredEmails.push(userEmail.toLowerCase());
        localStorage.setItem('registeredEmails', JSON.stringify(registeredEmails));

        // Store user data
        const userData = {
            name: userName,
            email: userEmail,
            registrationDate: new Date().toISOString()
        };
        localStorage.setItem('currentUser', JSON.stringify(userData));

        // Success message
        showMessage(`مرحباً ${userName}! تم تسجيل دخولك بنجاح`, 'success');

        // Close modal and reset form
        loginModal.style.display = 'none';
        loginForm.reset();

        // Redirect to app
        setTimeout(() => {
            showApp(userData);
        }, 1500);
    });
    
    // Notification items click handlers
    const notificationItems = document.querySelectorAll('.notification-item');
    notificationItems.forEach(item => {
        item.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'translateX(-10px) scale(1.05)';
            setTimeout(() => {
                this.style.transform = 'translateX(0) scale(1)';
            }, 200);
            
            const message = this.querySelector('span').textContent;
            showMessage(`تم النقر على: ${message}`, 'info');
        });
    });
    
    // Church click handler for blessing effect
    const church = document.querySelector('.church');
    church.addEventListener('click', function() {
        createEnhancedBlessingEffect();
        playChurchBells();
        showMessage('بركة الرب معكم! 🙏✝️', 'success');

        // Add special glow effect to church
        this.style.filter = 'brightness(1.3) drop-shadow(0 0 20px #FFD700)';
        setTimeout(() => {
            this.style.filter = '';
        }, 2000);
    });
    
    // Person walking click handler
    const personWalking = document.querySelector('.person-walking');
    personWalking.addEventListener('click', function() {
        // Speed up the walking animation temporarily
        this.style.animationDuration = '4s';
        setTimeout(() => {
            this.style.animationDuration = '8s';
        }, 4000);
        
        showMessage('الشخص يسرع في المشي إلى الكنيسة! 🚶‍♂️', 'info');
    });
    
    // Add floating hearts effect on page load
    setTimeout(() => {
        createFloatingHearts();
    }, 2000);

    // Add welcome sound effect (optional)
    playWelcomeSound();

    // Check if user is already logged in
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    if (currentUser) {
        updateUIForLoggedInUser(currentUser);
    }

    // Add church bell sound effect
    setTimeout(() => {
        playChurchBells();
    }, 3000);

    // Add candle click handlers
    const candles = document.querySelectorAll('.candle');
    candles.forEach((candle, index) => {
        candle.addEventListener('click', function() {
            // Light up the candle
            this.style.filter = 'brightness(1.5) drop-shadow(0 0 15px #FFA500)';
            showMessage(`تم إضاءة الشمعة ${index + 1} 🕯️`, 'success');

            // Create small blessing effect
            createSmallBlessingEffect(this);

            setTimeout(() => {
                this.style.filter = '';
            }, 3000);
        });
    });

    // Add church sign click handler
    const churchSign = document.querySelector('.church-sign');
    churchSign.addEventListener('click', function() {
        showMessage('كنيسة الشهيد العظيم مارجرجس والأنبا باخوميوس بالعصافرة ترحب بكم 🏛️', 'info');

        // Add glow effect to sign
        this.style.transform = 'translateX(-50%) scale(1.1)';
        this.style.filter = 'drop-shadow(0 0 20px #FFD700)';

        setTimeout(() => {
            this.style.transform = 'translateX(-50%) scale(1)';
            this.style.filter = '';
        }, 2000);
    });

    // Add tower bell click handler
    const towerBell = document.querySelector('.tower-bell');
    if (towerBell) {
        towerBell.addEventListener('click', function() {
            playChurchBells();
            showMessage('🔔 أجراس الكنيسة تدق!', 'success');

            // Enhanced bell animation
            this.style.animation = 'bellSwing 0.5s ease-in-out 6';
            setTimeout(() => {
                this.style.animation = 'bellSwing 4s ease-in-out infinite';
            }, 3000);
        });
    }
});

// Bible verses collection
const bibleVerses = [
    {
        text: "وَأَمَّا أَنَا فَقَدْ أَتَيْتُ لِتَكُونَ لَهُمْ حَيَاةٌ وَلِيَكُونَ لَهُمْ أَفْضَلُ",
        reference: "يوحنا 10: 10"
    },
    {
        text: "اِطْرَحُوا عَلَيْهِ كُلَّ هَمِّكُمْ لأَنَّهُ يَعْتَنِي بِكُمْ",
        reference: "بطرس الأولى 5: 7"
    },
    {
        text: "لاَ تَخَفْ لأَنِّي مَعَكَ. لاَ تَتَلَفَّتْ لأَنِّي إِلهُكَ. قَدْ أَيَّدْتُكَ وَأَعَنْتُكَ وَعَضَدْتُكَ بِيَمِينِ بِرِّي",
        reference: "إشعياء 41: 10"
    },
    {
        text: "كُلُّ الأَشْيَاءِ تَعْمَلُ مَعاً لِلْخَيْرِ لِلَّذِينَ يُحِبُّونَ اللهَ",
        reference: "رومية 8: 28"
    },
    {
        text: "أَسْتَطِيعُ كُلَّ شَيْءٍ فِي الْمَسِيحِ الَّذِي يُقَوِّينِي",
        reference: "فيلبي 4: 13"
    },
    {
        text: "سَلاَماً أَتْرُكُ لَكُمْ. سَلاَمِي أُعْطِيكُمْ. لَيْسَ كَمَا يُعْطِي الْعَالَمُ أُعْطِيكُمْ أَنَا",
        reference: "يوحنا 14: 27"
    },
    {
        text: "اُدْعُنِي فِي يَوْمِ الضِّيقِ أُنْقِذْكَ فَتُمَجِّدَنِي",
        reference: "مزمور 50: 15"
    },
    {
        text: "تَعَالَوْا إِلَيَّ يَا جَمِيعَ الْمُتْعَبِينَ وَالثَّقِيلِي الأَحْمَالِ، وَأَنَا أُرِيحُكُمْ",
        reference: "متى 11: 28"
    }
];

// Posts storage
let posts = JSON.parse(localStorage.getItem('posts')) || [];
let currentUser = null;

// Function to show the app after login
function showApp(userData) {
    currentUser = userData;

    // Hide welcome page
    document.getElementById('welcomePage').style.display = 'none';

    // Show app container and navigation
    document.getElementById('appContainer').style.display = 'block';
    document.getElementById('appNav').style.display = 'block';

    // Setup profile
    setupProfile(userData);

    // Load posts
    loadPosts();

    // Setup navigation
    setupNavigation();

    // Show daily verse
    displayDailyVerse();
}

// Function to setup profile
function setupProfile(userData) {
    document.getElementById('profileName').textContent = userData.name;

    // Check if user is developer (beshoy 586)
    if (userData.name.toLowerCase().includes('beshoy') && userData.name.includes('586')) {
        document.getElementById('verificationBadge').style.display = 'flex';
    }

    // Load saved profile data
    const profileData = JSON.parse(localStorage.getItem(`profile_${userData.email}`)) || {};

    if (profileData.bio) document.getElementById('profileBio').textContent = profileData.bio;
    if (profileData.education) document.getElementById('education').textContent = profileData.education;
    if (profileData.location) document.getElementById('location').textContent = profileData.location;
    if (profileData.church) document.getElementById('church').textContent = profileData.church;
    if (profileData.birthday) document.getElementById('birthday').textContent = profileData.birthday;
}

// Function to setup navigation
function setupNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all items
            navItems.forEach(nav => nav.classList.remove('active'));

            // Add active class to clicked item
            this.classList.add('active');

            // Hide all sections
            document.querySelectorAll('.app-section').forEach(section => {
                section.style.display = 'none';
            });

            // Show selected section
            const sectionId = this.dataset.section + 'Section';
            document.getElementById(sectionId).style.display = 'block';
        });
    });
}

// Function to display daily verse
function displayDailyVerse() {
    const today = new Date().getDate();
    const verseIndex = today % bibleVerses.length;
    const verse = bibleVerses[verseIndex];

    const verseElement = document.getElementById('dailyVerse');
    verseElement.innerHTML = `
        <p class="verse-text">"${verse.text}"</p>
        <span class="verse-ref">${verse.reference}</span>
    `;
}

// Function to get new verse
function getNewVerse() {
    const randomIndex = Math.floor(Math.random() * bibleVerses.length);
    const verse = bibleVerses[randomIndex];

    const verseElement = document.getElementById('dailyVerse');
    verseElement.innerHTML = `
        <p class="verse-text">"${verse.text}"</p>
        <span class="verse-ref">${verse.reference}</span>
    `;

    // Add animation
    verseElement.style.animation = 'fadeInUp 0.5s ease-out';
    setTimeout(() => {
        verseElement.style.animation = '';
    }, 500);
}

// Function to show messages
function showMessage(text, type = 'info') {
    // Remove existing message if any
    const existingMessage = document.querySelector('.floating-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // Create message element
    const message = document.createElement('div');
    message.className = `floating-message ${type}`;
    message.textContent = text;
    
    // Style the message
    message.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: ${type === 'success' ? '#2ecc71' : type === 'error' ? '#e74c3c' : '#3498db'};
        color: white;
        padding: 15px 25px;
        border-radius: 25px;
        font-size: 1.1rem;
        font-weight: bold;
        z-index: 10000;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        animation: messageSlideIn 0.5s ease-out;
        text-align: center;
        max-width: 80%;
    `;
    
    // Add CSS animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.5);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }
        
        @keyframes messageSlideOut {
            from {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
            to {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.5);
            }
        }
    `;
    document.head.appendChild(style);
    
    // Add to page
    document.body.appendChild(message);
    
    // Remove after 3 seconds
    setTimeout(() => {
        message.style.animation = 'messageSlideOut 0.5s ease-out';
        setTimeout(() => {
            message.remove();
        }, 500);
    }, 3000);
}

// Function to create blessing effect
function createBlessingEffect() {
    const blessings = ['✨', '🙏', '💫', '⭐', '🌟'];
    
    for (let i = 0; i < 10; i++) {
        setTimeout(() => {
            const blessing = document.createElement('div');
            blessing.textContent = blessings[Math.floor(Math.random() * blessings.length)];
            blessing.style.cssText = `
                position: absolute;
                font-size: 2rem;
                pointer-events: none;
                z-index: 1000;
                animation: blessingFloat 3s ease-out forwards;
            `;
            
            // Random position around the church
            const churchRect = document.querySelector('.church').getBoundingClientRect();
            blessing.style.left = (churchRect.left + Math.random() * churchRect.width) + 'px';
            blessing.style.top = (churchRect.top + Math.random() * churchRect.height) + 'px';
            
            document.body.appendChild(blessing);
            
            // Remove after animation
            setTimeout(() => {
                blessing.remove();
            }, 3000);
        }, i * 200);
    }
    
    // Add blessing animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes blessingFloat {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
            100% {
                opacity: 0;
                transform: translateY(-100px) scale(1.5);
            }
        }
    `;
    document.head.appendChild(style);
}

// Function to create floating hearts
function createFloatingHearts() {
    const hearts = ['❤️', '💙', '💚', '💛', '💜'];
    
    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            const heart = document.createElement('div');
            heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
            heart.style.cssText = `
                position: fixed;
                font-size: 1.5rem;
                pointer-events: none;
                z-index: 100;
                animation: heartFloat 6s ease-out forwards;
                left: ${Math.random() * 100}%;
                top: 100%;
            `;
            
            document.body.appendChild(heart);
            
            // Remove after animation
            setTimeout(() => {
                heart.remove();
            }, 6000);
        }, i * 1000);
    }
    
    // Add heart animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes heartFloat {
            0% {
                opacity: 1;
                transform: translateY(0) rotate(0deg);
            }
            100% {
                opacity: 0;
                transform: translateY(-100vh) rotate(360deg);
            }
        }
    `;
    document.head.appendChild(style);
}

// Function to play welcome sound (optional - requires audio file)
function playWelcomeSound() {
    // You can add an audio file and uncomment this
    /*
    const audio = new Audio('welcome-sound.mp3');
    audio.volume = 0.3;
    audio.play().catch(e => {
        console.log('Audio play failed:', e);
    });
    */
}

// Function to play church bells sound
function playChurchBells() {
    // Create bell sound effect using Web Audio API
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        function createBellTone(frequency, duration) {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        }

        // Play bell sequence
        createBellTone(523.25, 1.5); // C5
        setTimeout(() => createBellTone(659.25, 1.5), 500); // E5
        setTimeout(() => createBellTone(783.99, 1.5), 1000); // G5

    } catch (e) {
        console.log('Audio context not supported:', e);
    }
}

// Enhanced blessing effect with more variety
function createEnhancedBlessingEffect() {
    const blessings = ['✨', '🙏', '💫', '⭐', '🌟', '✝️', '🕊️', '💒', '🔔', '📿'];
    const colors = ['#FFD700', '#FFA500', '#FF6347', '#98FB98', '#87CEEB'];

    for (let i = 0; i < 15; i++) {
        setTimeout(() => {
            const blessing = document.createElement('div');
            blessing.textContent = blessings[Math.floor(Math.random() * blessings.length)];
            blessing.style.cssText = `
                position: fixed;
                font-size: ${1.5 + Math.random() * 1.5}rem;
                color: ${colors[Math.floor(Math.random() * colors.length)]};
                pointer-events: none;
                z-index: 1000;
                animation: enhancedBlessingFloat ${3 + Math.random() * 2}s ease-out forwards;
                text-shadow: 0 0 10px currentColor;
            `;

            // Random position around the church
            const churchRect = document.querySelector('.church').getBoundingClientRect();
            blessing.style.left = (churchRect.left + Math.random() * (churchRect.width + 100) - 50) + 'px';
            blessing.style.top = (churchRect.top + Math.random() * (churchRect.height + 50)) + 'px';

            document.body.appendChild(blessing);

            // Remove after animation
            setTimeout(() => {
                blessing.remove();
            }, 5000);
        }, i * 150);
    }

    // Add enhanced blessing animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes enhancedBlessingFloat {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1) rotate(0deg);
            }
            50% {
                opacity: 0.8;
                transform: translateY(-50px) scale(1.2) rotate(180deg);
            }
            100% {
                opacity: 0;
                transform: translateY(-150px) scale(1.5) rotate(360deg);
            }
        }
    `;
    document.head.appendChild(style);
}

// Function to create small blessing effect for candles
function createSmallBlessingEffect(element) {
    const blessings = ['✨', '🙏', '💫'];

    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            const blessing = document.createElement('div');
            blessing.textContent = blessings[Math.floor(Math.random() * blessings.length)];
            blessing.style.cssText = `
                position: fixed;
                font-size: 1rem;
                color: #FFD700;
                pointer-events: none;
                z-index: 1000;
                animation: smallBlessingFloat 2s ease-out forwards;
            `;

            const rect = element.getBoundingClientRect();
            blessing.style.left = (rect.left + Math.random() * rect.width) + 'px';
            blessing.style.top = (rect.top + Math.random() * rect.height) + 'px';

            document.body.appendChild(blessing);

            setTimeout(() => {
                blessing.remove();
            }, 2000);
        }, i * 100);
    }

    // Add small blessing animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes smallBlessingFloat {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
            100% {
                opacity: 0;
                transform: translateY(-50px) scale(1.2);
            }
        }
    `;
    document.head.appendChild(style);
}

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Press 'W' for WhatsApp
    if (e.key.toLowerCase() === 'w') {
        document.querySelector('.whatsapp-icon').click();
    }
    
    // Press 'L' for Login
    if (e.key.toLowerCase() === 'l') {
        document.querySelector('.login-icon').click();
    }
    
    // Press 'B' for Blessing
    if (e.key.toLowerCase() === 'b') {
        document.querySelector('.church').click();
    }
    
    // Press 'H' for Hearts
    if (e.key.toLowerCase() === 'h') {
        createFloatingHearts();
    }
});

// Add scroll effects
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const parallax = document.querySelector('.church-scene');
    const speed = scrolled * 0.5;
    
    if (parallax) {
        parallax.style.transform = `translateY(${speed}px)`;
    }
});

// Add mouse trail effect
document.addEventListener('mousemove', function(e) {
    // Create a small star that follows the mouse
    const star = document.createElement('div');
    star.textContent = '✨';
    star.style.cssText = `
        position: fixed;
        pointer-events: none;
        font-size: 1rem;
        z-index: 1;
        animation: starFade 1s ease-out forwards;
        left: ${e.clientX}px;
        top: ${e.clientY}px;
    `;
    
    document.body.appendChild(star);
    
    // Remove after animation
    setTimeout(() => {
        star.remove();
    }, 1000);
});

// Add star fade animation
const starStyle = document.createElement('style');
starStyle.textContent = `
    @keyframes starFade {
        0% {
            opacity: 1;
            transform: scale(1);
        }
        100% {
            opacity: 0;
            transform: scale(0.5);
        }
    }
`;
document.head.appendChild(starStyle);

// Function to create post
function createPost() {
    const postInput = document.getElementById('postInput');
    const content = postInput.value.trim();

    if (!content) {
        showMessage('يرجى كتابة شيء لنشره', 'error');
        return;
    }

    if (!currentUser) {
        showMessage('يجب تسجيل الدخول أولاً', 'error');
        return;
    }

    const post = {
        id: Date.now(),
        author: currentUser.name,
        authorEmail: currentUser.email,
        content: content,
        timestamp: new Date().toISOString(),
        likes: [],
        comments: [],
        prayers: []
    };

    posts.unshift(post);
    localStorage.setItem('posts', JSON.stringify(posts));

    postInput.value = '';
    loadPosts();
    showMessage('تم نشر المنشور بنجاح!', 'success');
}

// Function to load posts
function loadPosts() {
    const postsFeed = document.getElementById('postsFeed');
    if (!postsFeed) return;

    postsFeed.innerHTML = '';

    posts.forEach(post => {
        const postElement = createPostElement(post);
        postsFeed.appendChild(postElement);
    });
}

// Function to create post element
function createPostElement(post) {
    const postDiv = document.createElement('div');
    postDiv.className = 'post-item';
    postDiv.dataset.postId = post.id;

    const timeAgo = getTimeAgo(new Date(post.timestamp));
    const isVerified = post.author.toLowerCase().includes('beshoy') && post.author.includes('586');

    postDiv.innerHTML = `
        <div class="post-header">
            <div class="post-user">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-info">
                    <h4>${post.author} ${isVerified ? '<i class="fas fa-check-circle verified"></i>' : ''}</h4>
                    <span class="post-time">${timeAgo}</span>
                </div>
            </div>
        </div>
        <div class="post-content">
            <p>${post.content}</p>
        </div>
        <div class="post-interactions">
            <button class="interaction-btn like-btn ${post.likes.includes(currentUser?.email) ? 'active' : ''}" onclick="toggleLike(${post.id})">
                <i class="fas fa-heart"></i>
                <span>${post.likes.length}</span>
            </button>
            <button class="interaction-btn comment-btn" onclick="showComments(${post.id})">
                <i class="fas fa-comment"></i>
                <span>${post.comments.length}</span>
            </button>
            <button class="interaction-btn pray-btn ${post.prayers.includes(currentUser?.email) ? 'active' : ''}" onclick="togglePrayer(${post.id})">
                <i class="fas fa-praying-hands"></i>
                <span>${post.prayers.length}</span>
            </button>
            <button class="interaction-btn share-btn" onclick="sharePost(${post.id})">
                <i class="fas fa-share"></i>
                مشاركة
            </button>
        </div>
        <div class="post-comments" id="comments-${post.id}" style="display: none;">
            <div class="comments-list"></div>
            <div class="comment-input">
                <input type="text" placeholder="اكتب تعليق..." onkeypress="handleCommentKeypress(event, ${post.id})">
                <button onclick="addComment(${post.id})"><i class="fas fa-paper-plane"></i></button>
            </div>
        </div>
    `;

    return postDiv;
}

// Function to get time ago
function getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'الآن';
    if (diffInSeconds < 3600) return `منذ ${Math.floor(diffInSeconds / 60)} دقيقة`;
    if (diffInSeconds < 86400) return `منذ ${Math.floor(diffInSeconds / 3600)} ساعة`;
    return `منذ ${Math.floor(diffInSeconds / 86400)} يوم`;
}

// Function to toggle like
function toggleLike(postId) {
    if (!currentUser) return;

    const post = posts.find(p => p.id === postId);
    if (!post) return;

    const userEmail = currentUser.email;
    const likeIndex = post.likes.indexOf(userEmail);

    if (likeIndex > -1) {
        post.likes.splice(likeIndex, 1);
    } else {
        post.likes.push(userEmail);
        createHeartAnimation();
    }

    localStorage.setItem('posts', JSON.stringify(posts));
    loadPosts();
}

// Function to toggle prayer
function togglePrayer(postId) {
    if (!currentUser) return;

    const post = posts.find(p => p.id === postId);
    if (!post) return;

    const userEmail = currentUser.email;
    const prayerIndex = post.prayers.indexOf(userEmail);

    if (prayerIndex > -1) {
        post.prayers.splice(prayerIndex, 1);
    } else {
        post.prayers.push(userEmail);
        showMessage('تم إضافة صلاتك 🙏', 'success');
        createBlessingAnimation();
    }

    localStorage.setItem('posts', JSON.stringify(posts));
    loadPosts();
}

// Function to create heart animation
function createHeartAnimation() {
    const heart = document.createElement('div');
    heart.innerHTML = '❤️';
    heart.style.cssText = `
        position: fixed;
        font-size: 2rem;
        pointer-events: none;
        z-index: 10000;
        animation: heartFloat 2s ease-out forwards;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    `;

    document.body.appendChild(heart);

    setTimeout(() => {
        heart.remove();
    }, 2000);
}

// Function to create blessing animation
function createBlessingAnimation() {
    const blessing = document.createElement('div');
    blessing.innerHTML = '🙏';
    blessing.style.cssText = `
        position: fixed;
        font-size: 2rem;
        pointer-events: none;
        z-index: 10000;
        animation: blessingFloat 2s ease-out forwards;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    `;

    document.body.appendChild(blessing);

    setTimeout(() => {
        blessing.remove();
    }, 2000);
}

// Function to share verse
function shareVerse() {
    const randomIndex = Math.floor(Math.random() * bibleVerses.length);
    const verse = bibleVerses[randomIndex];

    const postInput = document.getElementById('postInput');
    postInput.value = `"${verse.text}" - ${verse.reference}`;

    showMessage('تم إضافة الآية! يمكنك إضافة تعليق وإنشاء المنشور', 'success');
}

// Function to request prayer
function requestPrayer() {
    const postInput = document.getElementById('postInput');
    postInput.value = '🙏 طلب صلاة: ';
    postInput.focus();

    showMessage('اكتب طلب الصلاة وسيصلي معك المجتمع', 'info');
}

// Function to logout
function logout() {
    if (confirm('هل تريد تسجيل الخروج؟')) {
        localStorage.removeItem('currentUser');
        location.reload();
    }
}

// Check if user is already logged in on page load
document.addEventListener('DOMContentLoaded', function() {
    const savedUser = JSON.parse(localStorage.getItem('currentUser'));
    if (savedUser) {
        showApp(savedUser);
    }
});
