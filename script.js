// Registered emails storage
let registeredEmails = JSON.parse(localStorage.getItem('registeredEmails')) || [];

// Wait for DOM to load
document.addEventListener('DOMContentLoaded', function() {

    // WhatsApp icon click handler
    const whatsappIcon = document.querySelector('.whatsapp-icon');
    whatsappIcon.addEventListener('click', function() {
        // Add click animation
        this.style.transform = 'scale(1.3)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 200);

        // Show WhatsApp message
        showMessage('سيتم توجيهك لواتساب الآن...', 'success');

        // Open WhatsApp with the specified number
        setTimeout(() => {
            window.open('https://wa.me/201147150701?text=مرحباً، أريد التواصل معكم من موقع برمجة المستقبل', '_blank');
        }, 1000);
    });

    // Login icon click handler
    const loginIcon = document.querySelector('.login-icon');
    const loginModal = document.getElementById('loginModal');
    const closeModal = document.querySelector('.close');

    loginIcon.addEventListener('click', function() {
        // Add click animation
        this.style.transform = 'scale(1.3)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 200);

        // Show login modal
        loginModal.style.display = 'block';
    });

    // Close modal handlers
    closeModal.addEventListener('click', function() {
        loginModal.style.display = 'none';
    });

    window.addEventListener('click', function(event) {
        if (event.target === loginModal) {
            loginModal.style.display = 'none';
        }
    });

    // Login form handler
    const loginForm = document.getElementById('loginForm');
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const userName = document.getElementById('userName').value.trim();
        const userEmail = document.getElementById('userEmail').value.trim();

        // Validate inputs
        if (!userName || !userEmail) {
            showMessage('يرجى ملء جميع الحقول', 'error');
            return;
        }

        // Check if email is already registered
        if (registeredEmails.includes(userEmail.toLowerCase())) {
            showMessage('هذا البريد الإلكتروني مسجل مسبقاً!', 'error');
            return;
        }

        // Register the email
        registeredEmails.push(userEmail.toLowerCase());
        localStorage.setItem('registeredEmails', JSON.stringify(registeredEmails));

        // Store user data
        const userData = {
            name: userName,
            email: userEmail,
            registrationDate: new Date().toISOString()
        };
        localStorage.setItem('currentUser', JSON.stringify(userData));

        // Success message
        showMessage(`مرحباً ${userName}! تم تسجيل دخولك بنجاح`, 'success');

        // Close modal and reset form
        loginModal.style.display = 'none';
        loginForm.reset();

        // Update UI to show logged in state
        updateUIForLoggedInUser(userData);
    });
    
    // Notification items click handlers
    const notificationItems = document.querySelectorAll('.notification-item');
    notificationItems.forEach(item => {
        item.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'translateX(-10px) scale(1.05)';
            setTimeout(() => {
                this.style.transform = 'translateX(0) scale(1)';
            }, 200);
            
            const message = this.querySelector('span').textContent;
            showMessage(`تم النقر على: ${message}`, 'info');
        });
    });
    
    // Church click handler for blessing effect
    const church = document.querySelector('.church');
    church.addEventListener('click', function() {
        createEnhancedBlessingEffect();
        playChurchBells();
        showMessage('بركة الرب معكم! 🙏✝️', 'success');

        // Add special glow effect to church
        this.style.filter = 'brightness(1.3) drop-shadow(0 0 20px #FFD700)';
        setTimeout(() => {
            this.style.filter = '';
        }, 2000);
    });
    
    // Person walking click handler
    const personWalking = document.querySelector('.person-walking');
    personWalking.addEventListener('click', function() {
        // Speed up the walking animation temporarily
        this.style.animationDuration = '4s';
        setTimeout(() => {
            this.style.animationDuration = '8s';
        }, 4000);
        
        showMessage('الشخص يسرع في المشي إلى الكنيسة! 🚶‍♂️', 'info');
    });
    
    // Add floating hearts effect on page load
    setTimeout(() => {
        createFloatingHearts();
    }, 2000);

    // Add welcome sound effect (optional)
    playWelcomeSound();

    // Check if user is already logged in
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    if (currentUser) {
        updateUIForLoggedInUser(currentUser);
    }

    // Add church bell sound effect
    setTimeout(() => {
        playChurchBells();
    }, 3000);

    // Add candle click handlers
    const candles = document.querySelectorAll('.candle');
    candles.forEach((candle, index) => {
        candle.addEventListener('click', function() {
            // Light up the candle
            this.style.filter = 'brightness(1.5) drop-shadow(0 0 15px #FFA500)';
            showMessage(`تم إضاءة الشمعة ${index + 1} 🕯️`, 'success');

            // Create small blessing effect
            createSmallBlessingEffect(this);

            setTimeout(() => {
                this.style.filter = '';
            }, 3000);
        });
    });

    // Add church sign click handler
    const churchSign = document.querySelector('.church-sign');
    churchSign.addEventListener('click', function() {
        showMessage('كنيسة الشهيد العظيم مارجرجس والأنبا باخوميوس بالعصافرة ترحب بكم 🏛️', 'info');

        // Add glow effect to sign
        this.style.transform = 'translateX(-50%) scale(1.1)';
        this.style.filter = 'drop-shadow(0 0 20px #FFD700)';

        setTimeout(() => {
            this.style.transform = 'translateX(-50%) scale(1)';
            this.style.filter = '';
        }, 2000);
    });

    // Add tower bell click handler
    const towerBell = document.querySelector('.tower-bell');
    if (towerBell) {
        towerBell.addEventListener('click', function() {
            playChurchBells();
            showMessage('🔔 أجراس الكنيسة تدق!', 'success');

            // Enhanced bell animation
            this.style.animation = 'bellSwing 0.5s ease-in-out 6';
            setTimeout(() => {
                this.style.animation = 'bellSwing 4s ease-in-out infinite';
            }, 3000);
        });
    }
});

// Function to update UI for logged in user
function updateUIForLoggedInUser(userData) {
    const loginIcon = document.querySelector('.login-icon');
    loginIcon.innerHTML = '<i class="fas fa-user-check"></i>';
    loginIcon.title = `مرحباً ${userData.name}`;

    // Add logout functionality
    loginIcon.removeEventListener('click', showLoginModal);
    loginIcon.addEventListener('click', function() {
        if (confirm('هل تريد تسجيل الخروج؟')) {
            localStorage.removeItem('currentUser');
            location.reload();
        }
    });

    // Update welcome message
    const welcomeMessage = document.querySelector('.welcome-message h2');
    welcomeMessage.textContent = `أهلاً وسهلاً ${userData.name} في برمجة المستقبل`;
}

// Function to show messages
function showMessage(text, type = 'info') {
    // Remove existing message if any
    const existingMessage = document.querySelector('.floating-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // Create message element
    const message = document.createElement('div');
    message.className = `floating-message ${type}`;
    message.textContent = text;
    
    // Style the message
    message.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: ${type === 'success' ? '#2ecc71' : type === 'error' ? '#e74c3c' : '#3498db'};
        color: white;
        padding: 15px 25px;
        border-radius: 25px;
        font-size: 1.1rem;
        font-weight: bold;
        z-index: 10000;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        animation: messageSlideIn 0.5s ease-out;
        text-align: center;
        max-width: 80%;
    `;
    
    // Add CSS animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.5);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }
        
        @keyframes messageSlideOut {
            from {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
            to {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.5);
            }
        }
    `;
    document.head.appendChild(style);
    
    // Add to page
    document.body.appendChild(message);
    
    // Remove after 3 seconds
    setTimeout(() => {
        message.style.animation = 'messageSlideOut 0.5s ease-out';
        setTimeout(() => {
            message.remove();
        }, 500);
    }, 3000);
}

// Function to create blessing effect
function createBlessingEffect() {
    const blessings = ['✨', '🙏', '💫', '⭐', '🌟'];
    
    for (let i = 0; i < 10; i++) {
        setTimeout(() => {
            const blessing = document.createElement('div');
            blessing.textContent = blessings[Math.floor(Math.random() * blessings.length)];
            blessing.style.cssText = `
                position: absolute;
                font-size: 2rem;
                pointer-events: none;
                z-index: 1000;
                animation: blessingFloat 3s ease-out forwards;
            `;
            
            // Random position around the church
            const churchRect = document.querySelector('.church').getBoundingClientRect();
            blessing.style.left = (churchRect.left + Math.random() * churchRect.width) + 'px';
            blessing.style.top = (churchRect.top + Math.random() * churchRect.height) + 'px';
            
            document.body.appendChild(blessing);
            
            // Remove after animation
            setTimeout(() => {
                blessing.remove();
            }, 3000);
        }, i * 200);
    }
    
    // Add blessing animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes blessingFloat {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
            100% {
                opacity: 0;
                transform: translateY(-100px) scale(1.5);
            }
        }
    `;
    document.head.appendChild(style);
}

// Function to create floating hearts
function createFloatingHearts() {
    const hearts = ['❤️', '💙', '💚', '💛', '💜'];
    
    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            const heart = document.createElement('div');
            heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
            heart.style.cssText = `
                position: fixed;
                font-size: 1.5rem;
                pointer-events: none;
                z-index: 100;
                animation: heartFloat 6s ease-out forwards;
                left: ${Math.random() * 100}%;
                top: 100%;
            `;
            
            document.body.appendChild(heart);
            
            // Remove after animation
            setTimeout(() => {
                heart.remove();
            }, 6000);
        }, i * 1000);
    }
    
    // Add heart animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes heartFloat {
            0% {
                opacity: 1;
                transform: translateY(0) rotate(0deg);
            }
            100% {
                opacity: 0;
                transform: translateY(-100vh) rotate(360deg);
            }
        }
    `;
    document.head.appendChild(style);
}

// Function to play welcome sound (optional - requires audio file)
function playWelcomeSound() {
    // You can add an audio file and uncomment this
    /*
    const audio = new Audio('welcome-sound.mp3');
    audio.volume = 0.3;
    audio.play().catch(e => {
        console.log('Audio play failed:', e);
    });
    */
}

// Function to play church bells sound
function playChurchBells() {
    // Create bell sound effect using Web Audio API
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        function createBellTone(frequency, duration) {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        }

        // Play bell sequence
        createBellTone(523.25, 1.5); // C5
        setTimeout(() => createBellTone(659.25, 1.5), 500); // E5
        setTimeout(() => createBellTone(783.99, 1.5), 1000); // G5

    } catch (e) {
        console.log('Audio context not supported:', e);
    }
}

// Enhanced blessing effect with more variety
function createEnhancedBlessingEffect() {
    const blessings = ['✨', '🙏', '💫', '⭐', '🌟', '✝️', '🕊️', '💒', '🔔', '📿'];
    const colors = ['#FFD700', '#FFA500', '#FF6347', '#98FB98', '#87CEEB'];

    for (let i = 0; i < 15; i++) {
        setTimeout(() => {
            const blessing = document.createElement('div');
            blessing.textContent = blessings[Math.floor(Math.random() * blessings.length)];
            blessing.style.cssText = `
                position: fixed;
                font-size: ${1.5 + Math.random() * 1.5}rem;
                color: ${colors[Math.floor(Math.random() * colors.length)]};
                pointer-events: none;
                z-index: 1000;
                animation: enhancedBlessingFloat ${3 + Math.random() * 2}s ease-out forwards;
                text-shadow: 0 0 10px currentColor;
            `;

            // Random position around the church
            const churchRect = document.querySelector('.church').getBoundingClientRect();
            blessing.style.left = (churchRect.left + Math.random() * (churchRect.width + 100) - 50) + 'px';
            blessing.style.top = (churchRect.top + Math.random() * (churchRect.height + 50)) + 'px';

            document.body.appendChild(blessing);

            // Remove after animation
            setTimeout(() => {
                blessing.remove();
            }, 5000);
        }, i * 150);
    }

    // Add enhanced blessing animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes enhancedBlessingFloat {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1) rotate(0deg);
            }
            50% {
                opacity: 0.8;
                transform: translateY(-50px) scale(1.2) rotate(180deg);
            }
            100% {
                opacity: 0;
                transform: translateY(-150px) scale(1.5) rotate(360deg);
            }
        }
    `;
    document.head.appendChild(style);
}

// Function to create small blessing effect for candles
function createSmallBlessingEffect(element) {
    const blessings = ['✨', '🙏', '💫'];

    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            const blessing = document.createElement('div');
            blessing.textContent = blessings[Math.floor(Math.random() * blessings.length)];
            blessing.style.cssText = `
                position: fixed;
                font-size: 1rem;
                color: #FFD700;
                pointer-events: none;
                z-index: 1000;
                animation: smallBlessingFloat 2s ease-out forwards;
            `;

            const rect = element.getBoundingClientRect();
            blessing.style.left = (rect.left + Math.random() * rect.width) + 'px';
            blessing.style.top = (rect.top + Math.random() * rect.height) + 'px';

            document.body.appendChild(blessing);

            setTimeout(() => {
                blessing.remove();
            }, 2000);
        }, i * 100);
    }

    // Add small blessing animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes smallBlessingFloat {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
            100% {
                opacity: 0;
                transform: translateY(-50px) scale(1.2);
            }
        }
    `;
    document.head.appendChild(style);
}

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Press 'W' for WhatsApp
    if (e.key.toLowerCase() === 'w') {
        document.querySelector('.whatsapp-icon').click();
    }
    
    // Press 'L' for Login
    if (e.key.toLowerCase() === 'l') {
        document.querySelector('.login-icon').click();
    }
    
    // Press 'B' for Blessing
    if (e.key.toLowerCase() === 'b') {
        document.querySelector('.church').click();
    }
    
    // Press 'H' for Hearts
    if (e.key.toLowerCase() === 'h') {
        createFloatingHearts();
    }
});

// Add scroll effects
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const parallax = document.querySelector('.church-scene');
    const speed = scrolled * 0.5;
    
    if (parallax) {
        parallax.style.transform = `translateY(${speed}px)`;
    }
});

// Add mouse trail effect
document.addEventListener('mousemove', function(e) {
    // Create a small star that follows the mouse
    const star = document.createElement('div');
    star.textContent = '✨';
    star.style.cssText = `
        position: fixed;
        pointer-events: none;
        font-size: 1rem;
        z-index: 1;
        animation: starFade 1s ease-out forwards;
        left: ${e.clientX}px;
        top: ${e.clientY}px;
    `;
    
    document.body.appendChild(star);
    
    // Remove after animation
    setTimeout(() => {
        star.remove();
    }, 1000);
});

// Add star fade animation
const starStyle = document.createElement('style');
starStyle.textContent = `
    @keyframes starFade {
        0% {
            opacity: 1;
            transform: scale(1);
        }
        100% {
            opacity: 0;
            transform: scale(0.5);
        }
    }
`;
document.head.appendChild(starStyle);
