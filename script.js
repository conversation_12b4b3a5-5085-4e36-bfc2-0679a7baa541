// Wait for DOM to load
document.addEventListener('DOMContentLoaded', function() {
    
    // WhatsApp icon click handler
    const whatsappIcon = document.querySelector('.whatsapp-icon');
    whatsappIcon.addEventListener('click', function() {
        // Add click animation
        this.style.transform = 'scale(1.3)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 200);
        
        // Show WhatsApp message
        showMessage('مرحباً! يمكنك التواصل معنا عبر واتساب', 'success');
        
        // Optional: Open WhatsApp (uncomment if you want to add a real WhatsApp link)
        // window.open('https://wa.me/YOUR_PHONE_NUMBER', '_blank');
    });
    
    // Login icon click handler
    const loginIcon = document.querySelector('.login-icon');
    loginIcon.addEventListener('click', function() {
        // Add click animation
        this.style.transform = 'scale(1.3)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 200);
        
        // Show login message
        showMessage('سيتم توجيهك لصفحة تسجيل الدخول قريباً', 'info');
    });
    
    // Notification items click handlers
    const notificationItems = document.querySelectorAll('.notification-item');
    notificationItems.forEach(item => {
        item.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'translateX(-10px) scale(1.05)';
            setTimeout(() => {
                this.style.transform = 'translateX(0) scale(1)';
            }, 200);
            
            const message = this.querySelector('span').textContent;
            showMessage(`تم النقر على: ${message}`, 'info');
        });
    });
    
    // Church click handler for blessing effect
    const church = document.querySelector('.church');
    church.addEventListener('click', function() {
        createBlessingEffect();
        showMessage('بركة الرب معكم! 🙏', 'success');
    });
    
    // Person walking click handler
    const personWalking = document.querySelector('.person-walking');
    personWalking.addEventListener('click', function() {
        // Speed up the walking animation temporarily
        this.style.animationDuration = '4s';
        setTimeout(() => {
            this.style.animationDuration = '8s';
        }, 4000);
        
        showMessage('الشخص يسرع في المشي إلى الكنيسة! 🚶‍♂️', 'info');
    });
    
    // Add floating hearts effect on page load
    setTimeout(() => {
        createFloatingHearts();
    }, 2000);
    
    // Add welcome sound effect (optional)
    playWelcomeSound();
});

// Function to show messages
function showMessage(text, type = 'info') {
    // Remove existing message if any
    const existingMessage = document.querySelector('.floating-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // Create message element
    const message = document.createElement('div');
    message.className = `floating-message ${type}`;
    message.textContent = text;
    
    // Style the message
    message.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: ${type === 'success' ? '#2ecc71' : type === 'error' ? '#e74c3c' : '#3498db'};
        color: white;
        padding: 15px 25px;
        border-radius: 25px;
        font-size: 1.1rem;
        font-weight: bold;
        z-index: 10000;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        animation: messageSlideIn 0.5s ease-out;
        text-align: center;
        max-width: 80%;
    `;
    
    // Add CSS animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.5);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }
        
        @keyframes messageSlideOut {
            from {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
            to {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.5);
            }
        }
    `;
    document.head.appendChild(style);
    
    // Add to page
    document.body.appendChild(message);
    
    // Remove after 3 seconds
    setTimeout(() => {
        message.style.animation = 'messageSlideOut 0.5s ease-out';
        setTimeout(() => {
            message.remove();
        }, 500);
    }, 3000);
}

// Function to create blessing effect
function createBlessingEffect() {
    const blessings = ['✨', '🙏', '💫', '⭐', '🌟'];
    
    for (let i = 0; i < 10; i++) {
        setTimeout(() => {
            const blessing = document.createElement('div');
            blessing.textContent = blessings[Math.floor(Math.random() * blessings.length)];
            blessing.style.cssText = `
                position: absolute;
                font-size: 2rem;
                pointer-events: none;
                z-index: 1000;
                animation: blessingFloat 3s ease-out forwards;
            `;
            
            // Random position around the church
            const churchRect = document.querySelector('.church').getBoundingClientRect();
            blessing.style.left = (churchRect.left + Math.random() * churchRect.width) + 'px';
            blessing.style.top = (churchRect.top + Math.random() * churchRect.height) + 'px';
            
            document.body.appendChild(blessing);
            
            // Remove after animation
            setTimeout(() => {
                blessing.remove();
            }, 3000);
        }, i * 200);
    }
    
    // Add blessing animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes blessingFloat {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
            100% {
                opacity: 0;
                transform: translateY(-100px) scale(1.5);
            }
        }
    `;
    document.head.appendChild(style);
}

// Function to create floating hearts
function createFloatingHearts() {
    const hearts = ['❤️', '💙', '💚', '💛', '💜'];
    
    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            const heart = document.createElement('div');
            heart.textContent = hearts[Math.floor(Math.random() * hearts.length)];
            heart.style.cssText = `
                position: fixed;
                font-size: 1.5rem;
                pointer-events: none;
                z-index: 100;
                animation: heartFloat 6s ease-out forwards;
                left: ${Math.random() * 100}%;
                top: 100%;
            `;
            
            document.body.appendChild(heart);
            
            // Remove after animation
            setTimeout(() => {
                heart.remove();
            }, 6000);
        }, i * 1000);
    }
    
    // Add heart animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes heartFloat {
            0% {
                opacity: 1;
                transform: translateY(0) rotate(0deg);
            }
            100% {
                opacity: 0;
                transform: translateY(-100vh) rotate(360deg);
            }
        }
    `;
    document.head.appendChild(style);
}

// Function to play welcome sound (optional - requires audio file)
function playWelcomeSound() {
    // You can add an audio file and uncomment this
    /*
    const audio = new Audio('welcome-sound.mp3');
    audio.volume = 0.3;
    audio.play().catch(e => {
        console.log('Audio play failed:', e);
    });
    */
}

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Press 'W' for WhatsApp
    if (e.key.toLowerCase() === 'w') {
        document.querySelector('.whatsapp-icon').click();
    }
    
    // Press 'L' for Login
    if (e.key.toLowerCase() === 'l') {
        document.querySelector('.login-icon').click();
    }
    
    // Press 'B' for Blessing
    if (e.key.toLowerCase() === 'b') {
        document.querySelector('.church').click();
    }
    
    // Press 'H' for Hearts
    if (e.key.toLowerCase() === 'h') {
        createFloatingHearts();
    }
});

// Add scroll effects
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const parallax = document.querySelector('.church-scene');
    const speed = scrolled * 0.5;
    
    if (parallax) {
        parallax.style.transform = `translateY(${speed}px)`;
    }
});

// Add mouse trail effect
document.addEventListener('mousemove', function(e) {
    // Create a small star that follows the mouse
    const star = document.createElement('div');
    star.textContent = '✨';
    star.style.cssText = `
        position: fixed;
        pointer-events: none;
        font-size: 1rem;
        z-index: 1;
        animation: starFade 1s ease-out forwards;
        left: ${e.clientX}px;
        top: ${e.clientY}px;
    `;
    
    document.body.appendChild(star);
    
    // Remove after animation
    setTimeout(() => {
        star.remove();
    }, 1000);
});

// Add star fade animation
const starStyle = document.createElement('style');
starStyle.textContent = `
    @keyframes starFade {
        0% {
            opacity: 1;
            transform: scale(1);
        }
        100% {
            opacity: 0;
            transform: scale(0.5);
        }
    }
`;
document.head.appendChild(starStyle);
